// User and Authentication Types
export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'admin' | 'teacher' | 'staff'

export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

// Login Form Types
export interface LoginFormData {
  username: string
  password: string
  rememberMe: boolean
}

export interface LoginResponse {
  success: boolean
  user?: User
  token?: string
  message?: string
  error?: string
}

// Student Types
export interface Student {
  id: string
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  phone?: string
  dateOfBirth: Date
  address: string
  course: string
  yearLevel: number
  section: string
  status: StudentStatus
  qrCode: string
  avatar?: string
  guardianName?: string
  guardianPhone?: string
  enrollmentDate: Date
  createdAt: Date
  updatedAt: Date
}

export type StudentStatus = 'active' | 'inactive' | 'graduated' | 'dropped'

export interface StudentFormData {
  studentId: string
  firstName: string
  lastName: string
  middleName?: string
  email?: string
  phone?: string
  dateOfBirth: string
  address: string
  course: string
  yearLevel: number
  section: string
  guardianName?: string
  guardianPhone?: string
}

// Attendance Types
export interface Attendance {
  id: string
  studentId: string
  student?: Student
  date: Date
  timeIn?: Date
  timeOut?: Date
  status: AttendanceStatus
  location?: string
  notes?: string
  scannedBy: string
  scannedByUser?: User
  createdAt: Date
  updatedAt: Date
}

export type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused'

export interface AttendanceRecord {
  date: string
  status: AttendanceStatus
  timeIn?: string
  timeOut?: string
  notes?: string
}

export interface AttendanceSummary {
  totalDays: number
  presentDays: number
  absentDays: number
  lateDays: number
  excusedDays: number
  attendanceRate: number
}

// Course and Academic Types
export interface Course {
  id: string
  code: string
  name: string
  description?: string
  department: string
  credits: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Section {
  id: string
  name: string
  course: string
  yearLevel: number
  capacity: number
  currentEnrollment: number
  adviser?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Analytics and Reports Types
export interface DashboardStats {
  totalStudents: number
  presentToday: number
  absentToday: number
  lateToday: number
  attendanceRate: number
  totalCourses: number
  activeSections: number
}

// Enhanced Dashboard Types
export interface WeatherData {
  temperature: number
  condition: string
  humidity: number
  icon: string
}

export interface RecentActivity {
  id: string
  studentName: string
  studentId: string
  action: 'check-in' | 'check-out' | 'late-arrival'
  time: string
  course: string
  location?: string
  avatar?: string
}

export interface AtRiskStudent {
  id: string
  name: string
  studentId: string
  course: string
  attendanceRate: number
  consecutiveAbsences: number
  lastAttendance: string
  riskLevel: 'high' | 'medium' | 'low'
}

export interface UpcomingClass {
  id: string
  subject: string
  instructor: string
  time: string
  room: string
  course: string
  expectedStudents: number
}

export interface SystemNotification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: string
  isRead: boolean
  actionRequired?: boolean
}

export interface AttendanceTrendData {
  date: string
  present: number
  absent: number
  late: number
  attendanceRate: number
}

export interface AttendanceAnalytics {
  dailyAttendance: DailyAttendanceData[]
  weeklyTrends: WeeklyTrendData[]
  monthlyStats: MonthlyStatsData[]
  courseWiseAttendance: CourseAttendanceData[]
}

export interface DailyAttendanceData {
  date: string
  present: number
  absent: number
  late: number
  total: number
}

export interface WeeklyTrendData {
  week: string
  attendanceRate: number
  totalStudents: number
}

export interface MonthlyStatsData {
  month: string
  year: number
  averageAttendance: number
  totalClasses: number
}

export interface CourseAttendanceData {
  course: string
  attendanceRate: number
  totalStudents: number
  presentStudents: number
}

// Enhanced Analytics Types for Dashboard
export type TimePeriod = 'daily' | 'weekly' | 'monthly' | 'custom'

export interface TimePeriodFilter {
  period: TimePeriod
  startDate?: Date
  endDate?: Date
  customRange?: {
    from: Date | undefined
    to: Date | undefined
  }
}

export interface AnalyticsKPI {
  id: string
  title: string
  value: number | string
  previousValue?: number | string
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  format?: 'percentage' | 'number' | 'currency' | 'time'
  icon?: string
  color?: 'green' | 'red' | 'blue' | 'yellow' | 'purple'
}

export interface AttendanceTrendPoint {
  date: string
  present: number
  absent: number
  late: number
  excused: number
  total: number
  attendanceRate: number
  dayOfWeek?: string
  month?: string
  week?: number
}

export interface GradeAttendanceData {
  grade: string
  section: string
  totalStudents: number
  presentStudents: number
  absentStudents: number
  lateStudents: number
  attendanceRate: number
  trend?: number
}

export interface AttendanceStatusDistribution {
  status: AttendanceStatus
  count: number
  percentage: number
  color: string
}

export interface HeatmapData {
  day: string
  hour: number
  value: number
  date: string
}

export interface PeakAttendanceHour {
  hour: string
  count: number
  percentage: number
}

export interface AtRiskStudentDetailed extends AtRiskStudent {
  trends: AttendanceTrendPoint[]
  patterns: AttendancePattern[]
  recommendations: string[]
  interventions: Intervention[]
}

export interface AttendancePattern {
  type: 'chronic_absence' | 'late_pattern' | 'friday_absence' | 'monday_absence' | 'irregular'
  description: string
  frequency: number
  severity: 'low' | 'medium' | 'high'
  detected: Date
}

export interface Intervention {
  id: string
  type: 'counseling' | 'parent_contact' | 'academic_support' | 'health_check'
  description: string
  status: 'pending' | 'in_progress' | 'completed'
  assignedTo?: string
  dueDate?: Date
  createdAt: Date
}

export interface AIInsight {
  id: string
  type: 'prediction' | 'pattern' | 'recommendation' | 'alert'
  title: string
  description: string
  confidence: number
  impact: 'low' | 'medium' | 'high'
  category: 'attendance' | 'performance' | 'behavior' | 'risk'
  actionable: boolean
  actions?: string[]
  createdAt: Date
}

export interface PredictiveAnalytics {
  nextWeekPrediction: {
    expectedAttendanceRate: number
    confidence: number
    factors: string[]
  }
  monthlyForecast: AttendanceTrendPoint[]
  riskPredictions: {
    studentsAtRisk: number
    newRiskStudents: AtRiskStudent[]
    improvingStudents: Student[]
  }
}

export interface TopPerformer {
  id: string
  name: string
  studentId: string
  course: string
  section: string
  attendanceRate: number
  streak: number
  avatar?: string
  achievements: string[]
}

export interface AnalyticsFilter {
  courses: string[]
  sections: string[]
  yearLevels: number[]
  attendanceStatus: AttendanceStatus[]
  riskLevels: ('high' | 'medium' | 'low')[]
  dateRange: TimePeriodFilter
}

export interface ChartExportData {
  chartType: string
  title: string
  data: any[]
  metadata: {
    generatedAt: Date
    filters: AnalyticsFilter
    period: TimePeriodFilter
  }
}

export interface DrillDownData {
  level: 'overview' | 'course' | 'section' | 'student'
  context: {
    courseId?: string
    sectionId?: string
    studentId?: string
  }
  data: any
  breadcrumb: string[]
}

// QR Code and Scanning Types
export interface QRScanResult {
  studentId: string
  timestamp: Date
  location?: string
  isValid: boolean
  error?: string
}

export interface ScanSession {
  id: string
  startTime: Date
  endTime?: Date
  location: string
  scannedBy: string
  totalScans: number
  successfulScans: number
  failedScans: number
  isActive: boolean
}

// Enhanced Scanning Interface Types
export interface ScannedStudent {
  id: string
  student: Student
  attendance: Attendance
  timestamp: Date
  scanMethod: 'qr' | 'manual'
}

export interface ScannerSettings {
  soundEnabled: boolean
  batchMode: boolean
  autoMarkPresent: boolean
  offlineMode: boolean
  cameraPermission: 'granted' | 'denied' | 'prompt'
}

export interface OfflineQueueItem {
  id: string
  studentId: string
  action: AttendanceStatus
  timestamp: Date
  location?: string
  notes?: string
}

export interface ScannerState {
  isScanning: boolean
  isProcessing: boolean
  currentStudent: Student | null
  recentScans: ScannedStudent[]
  offlineQueue: OfflineQueueItem[]
  settings: ScannerSettings
  error: string | null
}

// Settings and Configuration Types
export interface AppSettings {
  schoolName: string
  schoolAddress: string
  schoolLogo?: string
  academicYear: string
  semester: string
  attendanceGracePeriod: number // minutes
  autoLogoutTime: number // minutes
  enableNotifications: boolean
  defaultTheme: 'light' | 'dark' | 'system'
  timeZone: string
}

export interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  attendanceAlerts: boolean
  weeklyReports: boolean
  monthlyReports: boolean
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form and UI Types
export interface FormState {
  isLoading: boolean
  errors: Record<string, string>
  isDirty: boolean
}

export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
}

export interface FilterOptions {
  course?: string
  section?: string
  yearLevel?: number
  status?: StudentStatus | AttendanceStatus
  dateRange?: {
    start: Date
    end: Date
  }
}

// Navigation and Layout Types
export interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  children?: NavItem[]
}

export interface BreadcrumbItem {
  title: string
  href?: string
}

// Error and Loading Types
export interface ErrorState {
  message: string
  code?: string
  details?: any
}

export interface LoadingState {
  isLoading: boolean
  message?: string
}

// Student Management Types
export interface StudentTableColumn {
  key: keyof Student | 'photo' | 'actions'
  label: string
  sortable?: boolean
  width?: string
  render?: (value: any, student: Student) => React.ReactNode
}

export interface StudentFilters {
  search?: string
  grade?: string
  section?: string
  status?: StudentStatus
  course?: string
  yearLevel?: number
  enrollmentDateRange?: {
    start: Date
    end: Date
  }
}

export interface StudentSortConfig {
  field: keyof Student
  direction: 'asc' | 'desc'
}

export interface StudentPagination {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

export interface BulkAction {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: (studentIds: string[]) => Promise<void>
  requiresConfirmation?: boolean
  confirmationMessage?: string
}

export interface StudentStats {
  total: number
  active: number
  inactive: number
  newEnrollments: number
  graduated: number
  dropped: number
}

export interface StudentImportResult {
  success: boolean
  imported: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}

export interface StudentExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  fields: (keyof Student)[]
  filters?: StudentFilters
  includePhotos?: boolean
}

export interface GuardianInfo {
  name: string
  relationship: string
  phone: string
  email?: string
  address?: string
  occupation?: string
  emergencyContact: boolean
}

export interface AcademicRecord {
  semester: string
  academicYear: string
  gpa: number
  subjects: Array<{
    code: string
    name: string
    grade: string
    credits: number
  }>
  status: 'enrolled' | 'completed' | 'failed' | 'withdrawn'
}

export interface SMSNotification {
  id: string
  studentId: string
  recipientPhone: string
  message: string
  type: 'attendance' | 'grade' | 'announcement' | 'reminder'
  status: 'pending' | 'sent' | 'failed' | 'delivered'
  sentAt?: Date
  deliveredAt?: Date
  error?: string
}

export interface QRCodeData {
  studentId: string
  generatedAt: Date
  expiresAt?: Date
  isActive: boolean
  scanCount: number
  lastScanned?: Date
}

// Enhanced Student Interface
export interface EnhancedStudent extends Student {
  fullName: string
  guardians: GuardianInfo[]
  academicRecords: AcademicRecord[]
  attendanceSummary: AttendanceSummary
  smsNotifications: SMSNotification[]
  qrCodeData: QRCodeData
  profilePhoto?: string
  emergencyContacts: Array<{
    name: string
    phone: string
    relationship: string
  }>
}
